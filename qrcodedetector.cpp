#include "qrcodedetector.h"
#include <QDir>
#include <QFileInfo>
#include <QCoreApplication>
#include <QFile>
#include <fstream>
#include <vector>

QRCodeDetector::QRCodeDetector()
    : m_detector(nullptr), m_initialized(false), m_multiAngleEnabled(true), m_maxRotationAttempts(10), m_finderPatternEnabled(true)
{
}

QRCodeDetector::~QRCodeDetector()
{
    if (m_detector) {
        delete m_detector;
        m_detector = nullptr;
    }
}

bool QRCodeDetector::initialize(const QString& modelPath)
{
    try {
        m_initialized = false;
        m_lastError.clear();

        // 清理之前的检测器
        if (m_detector) {
            delete m_detector;
            m_detector = nullptr;
        }

        qDebug() << "Initializing WeChat QR code detector...";

        // 检查模型文件路径
        if (modelPath.isEmpty()) {
            setError("Model path is required for WeChat QR code detector");
            qWarning() << "Model path is required for WeChat QR code detector";
            return false;
        }

        // 构建模型文件路径
        QString detectModelPath = modelPath + "/detect.prototxt";
        QString srModelPath = modelPath + "/sr.prototxt";
        QString detectWeightsPath = modelPath + "/detect.caffemodel";
        QString srWeightsPath = modelPath + "/sr.caffemodel";

        // 检查模型文件是否存在
        QFileInfo detectModelFile(detectModelPath);
        QFileInfo srModelFile(srModelPath);
        QFileInfo detectWeightsFile(detectWeightsPath);
        QFileInfo srWeightsFile(srWeightsPath);

        if (!detectModelFile.exists()) {
            setError(QString("Detect model file not found: %1").arg(detectModelPath));
            return false;
        }
        if (!srModelFile.exists()) {
            setError(QString("SR model file not found: %1").arg(srModelPath));
            return false;
        }
        if (!detectWeightsFile.exists()) {
            setError(QString("Detect weights file not found: %1").arg(detectWeightsPath));
            return false;
        }
        if (!srWeightsFile.exists()) {
            setError(QString("SR weights file not found: %1").arg(srWeightsPath));
            return false;
        }

        // 初始化微信二维码检测器
        m_detector = new cv::wechat_qrcode::WeChatQRCode(
            detectModelPath.toStdString(),
            detectWeightsPath.toStdString(),
            srModelPath.toStdString(),
            srWeightsPath.toStdString()
        );

        m_initialized = true;
        qDebug() << "WeChat QR code detector initialized successfully";
        qDebug() << "Model path:" << modelPath;

        return true;

    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception:" << e.what();
        return false;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception:" << e.what();
        return false;
    } catch (...) {
        setError("Unknown exception");
        qWarning() << "Unknown exception";
        return false;
    }
}

QStringList QRCodeDetector::detectQRCodes(const QString& imagePath)
{
    QStringList results;
    
    if (!m_initialized) {
        setError("Detector not initialized");
        return results;
    }
    
    if (!QFileInfo::exists(imagePath)) {
        setError(QString("Image file does not exist: %1").arg(imagePath));
        return results;
    }
    
    try {
        qDebug() << "Starting QR code detection, image:" << imagePath;
        // 使用安全的方法读取图片（支持中文路径）
        cv::Mat image = readImageSafely(imagePath);
        if (image.empty()) {
            setError(QString("Unable to read image: %1").arg(imagePath));
            return results;
        }

        qDebug() << "Image size:" << image.cols << "x" << image.rows;

        return detectQRCodes(image);
        
    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception occurred during QR code detection:" << e.what();
        return results;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception occurred during QR code detection:" << e.what();
        return results;
    } catch (...) {
        setError("Unknown exception occurred during QR code detection");
        qWarning() << "Unknown exception occurred during QR code detection";
        return results;
    }
}

QStringList QRCodeDetector::detectQRCodes(const cv::Mat& image)
{
    QStringList results;

    if (!m_initialized) {
        setError("Detector not initialized");
        return results;
    }

    if (image.empty()) {
        setError("Input image is empty");
        return results;
    }

    try {
        // 第一次尝试：使用原始图片进行二维码检测
        qDebug() << "First attempt: detecting QR codes on original image";
        results = detectQRCodesInternal(image);

        // 如果第一次识别成功，直接返回结果
        if (!results.isEmpty()) {
            qDebug() << "QR code detected on first attempt";
            m_lastError.clear();
            return results;
        }

        // 第一次识别失败，优先尝试基于定位图案的智能角度检测
        if (m_finderPatternEnabled) {
            qDebug() << "First attempt failed, trying finder pattern-based detection";
            try {
                results = detectQRCodesWithFinderPatterns(image);

                // 如果定位图案检测成功，返回结果
                if (!results.isEmpty()) {
                    qDebug() << "QR code detected with finder pattern-based rotation";
                    m_lastError.clear();
                    return results;
                }
            } catch (const std::bad_alloc& e) {
                qWarning() << "Memory allocation failed in finder pattern detection, skipping";
                // 继续到下一个检测方法
            } catch (const std::exception& e) {
                qWarning() << "Exception in finder pattern detection:" << e.what();
            }
        }

        // 如果定位图案检测失败，根据配置决定是否尝试多角度旋转检测
        if (m_multiAngleEnabled) {
            qDebug() << "Finder pattern detection failed, trying multi-angle rotation detection";
            try {
                results = detectQRCodesWithRotation(image);

                // 如果旋转检测成功，返回结果
                if (!results.isEmpty()) {
                    qDebug() << "QR code detected with multi-angle rotation";
                    m_lastError.clear();
                    return results;
                }
            } catch (const std::bad_alloc& e) {
                qWarning() << "Memory allocation failed in multi-angle detection, skipping";
                // 继续到缩放检测
            } catch (const std::exception& e) {
                qWarning() << "Exception in multi-angle detection:" << e.what();
            }
        }

        // 最后尝试渐进式缩放策略
        qDebug() << "Smart detection failed, trying progressive scaling";

        // 先尝试2倍放大（性能更好）
        cv::Mat scaledImage2x;
        cv::resize(image, scaledImage2x, cv::Size(), 2.0, 2.0, cv::INTER_LINEAR);

        qDebug() << "Second attempt: 2x scaled image (" << scaledImage2x.cols << "x" << scaledImage2x.rows << ")";
        if (m_finderPatternEnabled) {
            results = detectQRCodesWithFinderPatterns(scaledImage2x);
        }
        if (results.isEmpty() && m_multiAngleEnabled) {
            results = detectQRCodesWithRotation(scaledImage2x);
        }
        if (results.isEmpty()) {
            results = detectQRCodesInternal(scaledImage2x);
        }

        // 如果2倍放大仍然失败，再尝试4倍放大
        // 添加详细的调试信息
        if (results.isEmpty()) {
            int totalPixels = image.cols * image.rows;
            qDebug() << "Image pixels:" << totalPixels << "(" << image.cols << "x" << image.rows << ")";
            qDebug() << "Checking if should try 4x scaling...";

            // 放宽限制：允许更大的图像进行4倍放大（8MP以下）
            if (totalPixels < 8000000) {
            qDebug() << "Second attempt failed, trying 4x scaled image";
            cv::Mat scaledImage4x;
            cv::resize(image, scaledImage4x, cv::Size(), 4.0, 4.0, cv::INTER_LINEAR);

            qDebug() << "Third attempt: 4x scaled image (" << scaledImage4x.cols << "x" << scaledImage4x.rows << ")";
            if (m_finderPatternEnabled) {
                results = detectQRCodesWithFinderPatterns(scaledImage4x);
            }
            if (results.isEmpty() && m_multiAngleEnabled) {
                results = detectQRCodesWithRotation(scaledImage4x);
            }
            if (results.isEmpty()) {
                results = detectQRCodesInternal(scaledImage4x);
            }
            } else {
                qDebug() << "Image too large for 4x scaling, skipping. Pixels:" << totalPixels;
            }
        } else {
            qDebug() << "2x scaling succeeded, no need for 4x scaling";
        }

        // 如果所有方法都失败，尝试简化检测作为最后手段
        if (results.isEmpty()) {
            qDebug() << "All advanced detection methods failed, trying simplified detection";
            try {
                results = detectQRCodesSimplified(image);
                if (!results.isEmpty()) {
                    qDebug() << "QR code detected with simplified method";
                    m_lastError.clear();
                    return results;
                }
            } catch (const std::exception& e) {
                qWarning() << "Exception in simplified detection:" << e.what();
            }
        }

        if (!results.isEmpty()) {
            qDebug() << "QR code detected with scaled image";
        } else {
            qDebug() << "No QR code detected in all attempts";
            setError("No QR codes detected after all attempts");
        }

        m_lastError.clear();
        return results;

    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception occurred during QR code detection:" << e.what();
        return results;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception occurred during QR code detection:" << e.what();
        return results;
    } catch (...) {
        setError("Unknown exception occurred during QR code detection");
        qWarning() << "Unknown exception occurred during QR code detection";
        return results;
    }
}

void QRCodeDetector::setError(const QString& error)
{
    m_lastError = error;
    qWarning() << "QRCodeDetector error:" << error;
}

cv::Mat QRCodeDetector::readImageSafely(const QString& imagePath)
{
    cv::Mat image;

    try {
        // 使用QFile读取文件内容到内存
        QFile file(imagePath);
        if (!file.open(QIODevice::ReadOnly)) {
            qWarning() << "Failed to open file:" << imagePath;
            return image;
        }

        QByteArray fileData = file.readAll();
        file.close();

        if (fileData.isEmpty()) {
            qWarning() << "File is empty:" << imagePath;
            return image;
        }

        // 将QByteArray转换为std::vector<uchar>
        std::vector<uchar> buffer(fileData.begin(), fileData.end());

        // 使用cv::imdecode解码图像数据
        image = cv::imdecode(buffer, cv::IMREAD_COLOR);

        if (image.empty()) {
            qWarning() << "Failed to decode image data from file:" << imagePath;
        } else {
            qDebug() << "Successfully loaded image from:" << imagePath
                     << "Size:" << image.cols << "x" << image.rows;
        }

    } catch (const std::exception& e) {
        qWarning() << "Exception while reading image file:" << imagePath
                   << "Error:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception while reading image file:" << imagePath;
    }

    return image;
}

QStringList QRCodeDetector::detectQRCodesInternal(const cv::Mat& image)
{
    QStringList results;

    if (image.empty()) {
        return results;
    }

    if (!m_detector) {
        qWarning() << "WeChat QR code detector not initialized";
        return results;
    }

    try {
        // 使用微信二维码检测器进行检测
        std::vector<cv::Mat> qrCodes;
        std::vector<cv::Mat> points;

        // 执行检测
        qrCodes = m_detector->detectAndDecode(image, points);

        if (!qrCodes.empty()) {
            qDebug() << "Found" << qrCodes.size() << "QR codes in image";

            for (size_t i = 0; i < qrCodes.size(); ++i) {
                if (!qrCodes[i].empty()) {
                    // 将cv::Mat转换为QString
                    std::string qrText(qrCodes[i].ptr<char>(), qrCodes[i].total());
                    QString qrCodeText = QString::fromStdString(qrText);

                    if (!qrCodeText.isEmpty()) {
                        results.append(qrCodeText);
                        qDebug() << "QR code" << (i + 1) << "detected";
                        qDebug() << "  Content:" << qrCodeText;

                        // 打印位置信息
                        if (i < points.size() && !points[i].empty()) {
                            qDebug() << "  Position points count:" << points[i].rows;
                            for (int j = 0; j < points[i].rows; ++j) {
                                cv::Point2f point = points[i].at<cv::Point2f>(j);
                                qDebug() << "    Point" << j << ": (" << point.x << "," << point.y << ")";
                            }
                        }
                    }
                }
            }
        } else {
            qDebug() << "No QR codes detected or failed to decode in this attempt";
        }

        qDebug() << "Detected" << results.size() << "QR codes in this attempt";

        /* ZXing代码已注释
        // 使用ZXing进行二维码检测
        // 将OpenCV Mat转换为ZXing可以处理的格式
        ZXing::ImageView imageView(image.data, image.cols, image.rows, ZXing::ImageFormat::BGR);

        // 设置检测选项 - 支持多种条码格式
        ZXing::ReaderOptions options;
        // 设置支持的条码格式：二维码 + 常用一维码
        options.setFormats(ZXing::BarcodeFormat::Any);           // PDF417二维码
        // options.setTryHarder(true);
        // options.setTryRotate(true);

        // 执行多条码检测
        auto barcodeResults = ZXing::ReadBarcodes(imageView, options);

        if (!barcodeResults.empty()) {
            qDebug() << "Found" << barcodeResults.size() << "barcodes in image";

            for (size_t i = 0; i < barcodeResults.size(); ++i) {
                const auto& result = barcodeResults[i];
                if (result.isValid()) {
                    QString barcodeText = QString::fromStdString(result.text());
                    QString formatName = QString::fromStdString(ZXing::ToString(result.format()));

                    if (!barcodeText.isEmpty()) {
                        results.append(barcodeText);
                        qDebug() << "Barcode" << (i + 1) << "detected";
                        qDebug() << "  Format:" << formatName;
                        qDebug() << "  Content:" << barcodeText;

                        // 打印位置信息
                        // auto position = result.position();
                        // if (!position.empty()) {
                        //     qDebug() << "  Position points count:" << position.size();
                        //     for (size_t j = 0; j < position.size(); ++j) {
                        //         qDebug() << "    Point" << j << ": (" << position[j].x << "," << position[j].y << ")";
                        //     }
                        // }
                    }
                }
            }
        } else {
            qDebug() << "No barcodes detected or failed to decode in this attempt";
        }

        qDebug() << "Detected" << results.size() << "barcodes in this attempt";
        */

    } catch (const cv::Exception& e) {
        qWarning() << "OpenCV exception in detectQRCodesInternal:" << e.what();
    } catch (const std::exception& e) {
        qWarning() << "Standard exception in detectQRCodesInternal:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception in detectQRCodesInternal";
    }

    return results;
}

QStringList QRCodeDetector::detectQRCodesWithFinderPatterns(const cv::Mat& image)
{
    QStringList results;

    if (image.empty()) {
        return results;
    }

    try {
        qDebug() << "Starting finder pattern-based QR detection";

        // 转换为灰度图像
        cv::Mat grayImage;
        if (image.channels() == 3) {
            cv::cvtColor(image, grayImage, cv::COLOR_BGR2GRAY);
        } else {
            grayImage = image.clone();
        }

        // 检测定位图案
        std::vector<cv::Point2f> finderCenters = detectFinderPatterns(grayImage);

        // 如果主要方法没有找到足够的定位图案，尝试简化方法
        if (finderCenters.size() < 2) {
            qDebug() << "Main finder pattern detection failed, trying simple method";
            finderCenters = detectFinderPatternsSimple(grayImage);
        }

        // 如果简化方法也失败，尝试模板匹配
        if (finderCenters.size() < 2) {
            qDebug() << "Simple finder pattern detection failed, trying template matching";
            finderCenters = detectFinderPatternsTemplate(grayImage);
        }

        if (finderCenters.size() >= 2) {
            qDebug() << "Found" << finderCenters.size() << "finder patterns";

            // 计算旋转角度
            double rotationAngle = calculateRotationAngle(finderCenters);
            qDebug() << "Calculated rotation angle:" << rotationAngle << "degrees";

            // 如果角度很小，直接使用原图
            if (std::abs(rotationAngle) < 2.0) {
                qDebug() << "Rotation angle is small, using original image";
                results = detectQRCodesSingleAttempt(image);
            } else {
                // 旋转图像并检测
                qDebug() << "Rotating image by" << rotationAngle << "degrees";
                cv::Mat rotatedImage = rotateImage(image, -rotationAngle); // 反向旋转校正

                if (!rotatedImage.empty()) {
                    results = detectQRCodesSingleAttempt(rotatedImage);

                    if (results.isEmpty()) {
                        // 尝试微调角度
                        qDebug() << "Trying fine-tuned angles";
                        std::vector<double> fineAngles = {-rotationAngle + 2, -rotationAngle - 2,
                                                         -rotationAngle + 5, -rotationAngle - 5};

                        for (double angle : fineAngles) {
                            try {
                                cv::Mat fineTunedImage = rotateImage(image, angle);
                                if (!fineTunedImage.empty()) {
                                    results = detectQRCodesSingleAttempt(fineTunedImage);
                                    if (!results.isEmpty()) {
                                        qDebug() << "QR code detected with fine-tuned angle:" << angle;
                                        break;
                                    }
                                    // 立即释放内存
                                    fineTunedImage.release();
                                }
                            } catch (const std::bad_alloc& e) {
                                qWarning() << "Memory allocation failed during fine-tuning at angle" << angle;
                                break; // 停止微调尝试
                            } catch (const std::exception& e) {
                                qWarning() << "Exception during fine-tuning at angle" << angle << ":" << e.what();
                                continue;
                            }
                        }
                    }
                }
            }
        } else {
            qDebug() << "Insufficient finder patterns detected (" << finderCenters.size() << "), falling back to multi-angle detection";
            qDebug() << "Image characteristics: size=" << image.cols << "x" << image.rows
                     << ", channels=" << image.channels();

            // 提供一些建议
            if (finderCenters.size() == 1) {
                qDebug() << "Found 1 finder pattern - image may be partially cropped or rotated significantly";
            } else if (finderCenters.size() == 0) {
                qDebug() << "No finder patterns found - image may be too blurry, low contrast, or not contain a QR code";
            }
        }

    } catch (const std::exception& e) {
        qWarning() << "Exception in finder pattern detection:" << e.what();
    }

    return results;
}

std::vector<cv::Point2f> QRCodeDetector::detectFinderPatterns(const cv::Mat& grayImage)
{
    std::vector<cv::Point2f> finderCenters;

    if (grayImage.empty()) {
        return finderCenters;
    }

    try {
        qDebug() << "Starting finder pattern detection on image:" << grayImage.cols << "x" << grayImage.rows;

        // 尝试多种阈值方法以提高检测率
        std::vector<cv::Mat> binaryImages;

        // 方法1：自适应阈值
        cv::Mat binaryImage1;
        cv::adaptiveThreshold(grayImage, binaryImage1, 255, cv::ADAPTIVE_THRESH_GAUSSIAN_C, cv::THRESH_BINARY, 11, 2);
        binaryImages.push_back(binaryImage1);

        // 方法2：不同参数的自适应阈值
        cv::Mat binaryImage2;
        cv::adaptiveThreshold(grayImage, binaryImage2, 255, cv::ADAPTIVE_THRESH_MEAN_C, cv::THRESH_BINARY, 15, 5);
        binaryImages.push_back(binaryImage2);

        // 方法3：Otsu阈值
        cv::Mat binaryImage3;
        cv::threshold(grayImage, binaryImage3, 0, 255, cv::THRESH_BINARY + cv::THRESH_OTSU);
        binaryImages.push_back(binaryImage3);

        // 对每种二值化方法尝试检测
        for (size_t methodIndex = 0; methodIndex < binaryImages.size(); methodIndex++) {
            qDebug() << "Trying binary method" << methodIndex + 1;

            // 查找轮廓
            std::vector<std::vector<cv::Point>> contours;
            std::vector<cv::Vec4i> hierarchy;
            cv::findContours(binaryImages[methodIndex], contours, hierarchy, cv::RETR_TREE, cv::CHAIN_APPROX_SIMPLE);

            qDebug() << "Found" << contours.size() << "contours with method" << methodIndex + 1;

            // 检查每个轮廓是否为定位图案
            for (size_t i = 0; i < contours.size(); i++) {
                if (isFinderPattern(contours[i], hierarchy, i)) {
                    // 计算轮廓的中心点
                    cv::Moments moments = cv::moments(contours[i]);
                    if (moments.m00 != 0) {
                        cv::Point2f center(moments.m10 / moments.m00, moments.m01 / moments.m00);
                        finderCenters.push_back(center);
                        qDebug() << "Finder pattern detected at:" << center.x << "," << center.y << "with method" << methodIndex + 1;
                    }
                }
            }

            // 如果已经找到足够的定位图案，可以提前退出
            if (finderCenters.size() >= 2) {
                qDebug() << "Found sufficient finder patterns with method" << methodIndex + 1;
                break;
            }
        }

        // 过滤重复的定位图案（距离太近的点）
        std::vector<cv::Point2f> filteredCenters;
        const double minDistance = 30.0; // 最小距离阈值

        for (const auto& center : finderCenters) {
            bool isDuplicate = false;
            for (const auto& existing : filteredCenters) {
                double distance = cv::norm(center - existing);
                if (distance < minDistance) {
                    isDuplicate = true;
                    break;
                }
            }
            if (!isDuplicate) {
                filteredCenters.push_back(center);
            }
        }

        qDebug() << "After filtering:" << filteredCenters.size() << "unique finder patterns";
        return filteredCenters;

    } catch (const std::exception& e) {
        qWarning() << "Exception in detectFinderPatterns:" << e.what();
    }

    return finderCenters;
}

bool QRCodeDetector::isFinderPattern(const std::vector<cv::Point>& contour,
                                    const std::vector<cv::Vec4i>& hierarchy,
                                    int contourIndex)
{
    try {
        // 检查轮廓面积 - 放宽限制
        double area = cv::contourArea(contour);
        if (area < 50 || area > 50000) { // 扩大面积范围
            return false;
        }

        // 检查长宽比 - 更宽松的限制
        cv::Rect boundingRect = cv::boundingRect(contour);
        if (boundingRect.width < 10 || boundingRect.height < 10) {
            return false; // 太小的轮廓
        }

        double aspectRatio = double(boundingRect.width) / double(boundingRect.height);
        if (aspectRatio < 0.3 || aspectRatio > 3.0) { // 更宽松的长宽比
            return false;
        }

        // 简化的形状检查 - 检查轮廓是否大致为矩形
        std::vector<cv::Point> approx;
        double epsilon = 0.05 * cv::arcLength(contour, true); // 更宽松的近似
        cv::approxPolyDP(contour, approx, epsilon, true);

        // 允许更多的顶点数量
        if (approx.size() < 3 || approx.size() > 12) {
            return false;
        }

        // 检查轮廓的凸性（定位图案通常是凸的）
        if (!cv::isContourConvex(approx)) {
            // 如果不是凸的，检查凸包面积比
            std::vector<cv::Point> hull;
            cv::convexHull(contour, hull);
            double hullArea = cv::contourArea(hull);
            if (hullArea > 0 && area / hullArea < 0.7) {
                return false; // 太不规则
            }
        }

        // 简化的层次结构检查
        // hierarchy[i] = [next, previous, first_child, parent]
        int childIndex = hierarchy[contourIndex][2]; // first_child
        int parentIndex = hierarchy[contourIndex][3]; // parent

        // 检查是否有嵌套结构（有子轮廓或父轮廓）
        bool hasNesting = (childIndex != -1) || (parentIndex != -1);

        if (!hasNesting) {
            // 如果没有嵌套结构，检查轮廓是否足够规则
            double perimeter = cv::arcLength(contour, true);
            double circularity = 4 * CV_PI * area / (perimeter * perimeter);

            // 定位图案应该相对规则（接近正方形）
            if (circularity < 0.3) { // 更宽松的圆形度要求
                return false;
            }
        }

        return true;

    } catch (const std::exception& e) {
        qWarning() << "Exception in isFinderPattern:" << e.what();
        return false;
    }
}

double QRCodeDetector::calculateRotationAngle(const std::vector<cv::Point2f>& finderCenters)
{
    if (finderCenters.size() < 2) {
        qWarning() << "Insufficient finder patterns for angle calculation";
        return 0.0;
    }

    try {
        // 如果有3个定位图案，找到最佳的两个点来计算角度
        cv::Point2f point1, point2;

        if (finderCenters.size() == 2) {
            point1 = finderCenters[0];
            point2 = finderCenters[1];
        } else if (finderCenters.size() >= 3) {
            // 找到距离最远的两个点（通常是对角的定位图案）
            double maxDistance = 0;
            int maxIndex1 = 0, maxIndex2 = 1;

            for (size_t i = 0; i < finderCenters.size(); i++) {
                for (size_t j = i + 1; j < finderCenters.size(); j++) {
                    double distance = cv::norm(finderCenters[i] - finderCenters[j]);
                    if (distance > maxDistance) {
                        maxDistance = distance;
                        maxIndex1 = i;
                        maxIndex2 = j;
                    }
                }
            }

            point1 = finderCenters[maxIndex1];
            point2 = finderCenters[maxIndex2];

            qDebug() << "Using finder patterns at (" << point1.x << "," << point1.y
                     << ") and (" << point2.x << "," << point2.y << ") for angle calculation";
        }

        // 计算两点之间的角度
        cv::Point2f vector = point2 - point1;
        double angleRadians = std::atan2(vector.y, vector.x);
        double angleDegrees = angleRadians * 180.0 / CV_PI;

        // 标准化角度到 [-90, 90] 范围
        // 二维码的定位图案通常在左上、右上、左下角
        // 我们希望将二维码旋转到标准方向
        while (angleDegrees > 90) angleDegrees -= 180;
        while (angleDegrees < -90) angleDegrees += 180;

        // 如果角度接近45度的倍数，可能需要调整
        // 因为二维码的定位图案可能不是完全水平或垂直排列的
        if (std::abs(angleDegrees - 45) < 10) {
            angleDegrees -= 45;
        } else if (std::abs(angleDegrees + 45) < 10) {
            angleDegrees += 45;
        }

        qDebug() << "Calculated rotation angle:" << angleDegrees << "degrees";
        return angleDegrees;

    } catch (const std::exception& e) {
        qWarning() << "Exception in calculateRotationAngle:" << e.what();
        return 0.0;
    }
}

QStringList QRCodeDetector::detectQRCodesWithRotation(const cv::Mat& image)
{
    QStringList results;

    if (image.empty()) {
        return results;
    }

    // 首先尝试原始图像（0度）
    qDebug() << "Trying original image (0 degrees)";
    results = detectQRCodesSingleAttempt(image);
    if (!results.isEmpty()) {
        qDebug() << "QR code detected at 0 degrees";
        return results;
    }

    // 定义要尝试的角度列表（以度为单位）
    // 优化后的角度序列：减少角度数量以节省内存
    std::vector<double> angles = {
        // 第一轮：最常见的角度
        90, 180, 270, 45, 135, 225, 315,
        // 第二轮：常见的小角度倾斜
        15, 30, 60, 75, -15, -30,
        // 第三轮：精细角度（减少数量）
        5, 10, 20, 25, 35, 40, -5, -10, -20, -25
    };

    // 限制图像大小以提高性能和减少内存使用
    cv::Mat workingImage;
    int maxDimension = 800; // 减小最大尺寸以节省内存
    if (image.cols > maxDimension || image.rows > maxDimension) {
        double scale = std::min(double(maxDimension) / image.cols, double(maxDimension) / image.rows);
        cv::resize(image, workingImage, cv::Size(), scale, scale, cv::INTER_LINEAR);
        qDebug() << "Resized image for rotation detection:" << workingImage.cols << "x" << workingImage.rows;
    } else {
        workingImage = image; // 直接使用原图，避免不必要的复制
    }

    // 尝试每个角度
    int attemptCount = 0;
    const int maxAttempts = m_maxRotationAttempts; // 使用配置的最大尝试次数

    for (double angle : angles) {
        if (++attemptCount > maxAttempts) {
            qDebug() << "Reached maximum rotation attempts (" << maxAttempts << "), stopping";
            break;
        }

        qDebug() << "Trying rotation angle:" << angle << "degrees (attempt" << attemptCount << "/" << maxAttempts << ")";

        try {
            cv::Mat rotatedImage = rotateImage(workingImage, angle);
            if (!rotatedImage.empty()) {
                results = detectQRCodesSingleAttempt(rotatedImage);
                if (!results.isEmpty()) {
                    qDebug() << "QR code detected at angle:" << angle << "degrees after" << attemptCount << "attempts";
                    return results;
                }
                // 立即释放旋转图像的内存
                rotatedImage.release();
            }
        } catch (const cv::Exception& e) {
            qWarning() << "OpenCV exception during rotation at angle" << angle << ":" << e.what();
            continue;
        } catch (const std::bad_alloc& e) {
            qWarning() << "Memory allocation failed at angle" << angle << ":" << e.what();
            qWarning() << "Stopping rotation attempts due to memory constraints";
            break;
        } catch (const std::exception& e) {
            qWarning() << "Exception during rotation at angle" << angle << ":" << e.what();
            continue;
        }
    }

    qDebug() << "No QR code detected after trying all rotation angles";
    return results;
}

cv::Mat QRCodeDetector::rotateImage(const cv::Mat& image, double angle)
{
    if (image.empty()) {
        return cv::Mat();
    }

    // 计算旋转中心
    cv::Point2f center(image.cols / 2.0, image.rows / 2.0);

    // 获取旋转矩阵
    cv::Mat rotationMatrix = cv::getRotationMatrix2D(center, angle, 1.0);

    // 计算旋转后的图像边界
    cv::Rect2f bbox = cv::RotatedRect(cv::Point2f(), image.size(), angle).boundingRect2f();

    // 调整旋转矩阵以确保整个旋转后的图像都在视野内
    rotationMatrix.at<double>(0, 2) += bbox.width / 2.0 - center.x;
    rotationMatrix.at<double>(1, 2) += bbox.height / 2.0 - center.y;

    // 执行旋转
    cv::Mat rotatedImage;
    cv::warpAffine(image, rotatedImage, rotationMatrix, bbox.size(), cv::INTER_LINEAR, cv::BORDER_CONSTANT, cv::Scalar(255, 255, 255));

    return rotatedImage;
}

QStringList QRCodeDetector::detectQRCodesSingleAttempt(const cv::Mat& image)
{
    QStringList results;

    if (image.empty()) {
        return results;
    }

    if (!m_detector) {
        qWarning() << "WeChat QR code detector not initialized";
        return results;
    }

    try {
        // 使用微信二维码检测器进行检测
        std::vector<cv::Mat> qrCodes;
        std::vector<cv::Mat> points;

        // 执行检测
        qrCodes = m_detector->detectAndDecode(image, points);

        for (const auto& qrCode : qrCodes) {
            if (!qrCode.empty()) {
                // 将cv::Mat转换为QString
                std::string qrText(qrCode.ptr<char>(), qrCode.total());
                QString qrCodeText = QString::fromStdString(qrText);
                if (!qrCodeText.isEmpty()) {
                    results.append(qrCodeText);
                }
            }
        }

        /* ZXing代码已注释
        // 使用ZXing进行二维码检测
        ZXing::ImageView imageView(image.data, image.cols, image.rows, ZXing::ImageFormat::BGR);

        // 设置检测选项
        ZXing::ReaderOptions options;
        options.setFormats(ZXing::BarcodeFormat::Any);
        options.setTryHarder(true);
        options.setTryRotate(false);  // 关闭自动旋转，因为我们手动处理旋转

        // 执行检测
        auto barcodeResults = ZXing::ReadBarcodes(imageView, options);

        for (const auto& result : barcodeResults) {
            if (result.isValid()) {
                QString barcodeText = QString::fromStdString(result.text());
                if (!barcodeText.isEmpty()) {
                    results.append(barcodeText);
                }
            }
        }
        */

    } catch (const std::exception& e) {
        qWarning() << "Exception in detectQRCodesSingleAttempt:" << e.what();
    }

    return results;
}

QStringList QRCodeDetector::detectQRCodesSimplified(const cv::Mat& image)
{
    QStringList results;

    if (image.empty()) {
        return results;
    }

    qDebug() << "Using simplified detection method (memory-friendly)";

    try {
        // 只尝试几个最常见的角度，不创建大量旋转图像
        std::vector<double> basicAngles = {0, 90, 180, 270, 45, 135};

        for (double angle : basicAngles) {
            try {
                if (angle == 0) {
                    // 直接使用原图
                    results = detectQRCodesSingleAttempt(image);
                } else {
                    // 创建小尺寸的旋转图像
                    cv::Mat smallImage;
                    double scale = std::min(400.0 / image.cols, 400.0 / image.rows);
                    if (scale < 1.0) {
                        cv::resize(image, smallImage, cv::Size(), scale, scale, cv::INTER_LINEAR);
                    } else {
                        smallImage = image;
                    }

                    cv::Mat rotatedImage = rotateImage(smallImage, angle);
                    if (!rotatedImage.empty()) {
                        results = detectQRCodesSingleAttempt(rotatedImage);
                        rotatedImage.release(); // 立即释放内存
                    }
                    if (scale < 1.0) {
                        smallImage.release(); // 释放缩放图像内存
                    }
                }

                if (!results.isEmpty()) {
                    qDebug() << "QR code detected with simplified method at angle:" << angle;
                    return results;
                }
            } catch (const std::bad_alloc& e) {
                qWarning() << "Memory allocation failed in simplified detection at angle" << angle;
                break; // 停止尝试
            }
        }

    } catch (const std::exception& e) {
        qWarning() << "Exception in simplified detection:" << e.what();
    }

    return results;
}

std::vector<cv::Point2f> QRCodeDetector::detectFinderPatternsSimple(const cv::Mat& grayImage)
{
    std::vector<cv::Point2f> finderCenters;

    if (grayImage.empty()) {
        return finderCenters;
    }

    try {
        qDebug() << "Starting simple finder pattern detection";

        // 使用简单的阈值
        cv::Mat binaryImage;
        cv::threshold(grayImage, binaryImage, 0, 255, cv::THRESH_BINARY + cv::THRESH_OTSU);

        // 查找轮廓
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(binaryImage, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

        qDebug() << "Found" << contours.size() << "contours for simple detection";

        // 寻找方形轮廓
        for (const auto& contour : contours) {
            double area = cv::contourArea(contour);
            if (area < 100 || area > 20000) {
                continue;
            }

            // 检查轮廓是否接近正方形
            cv::Rect boundingRect = cv::boundingRect(contour);
            double aspectRatio = double(boundingRect.width) / double(boundingRect.height);

            if (aspectRatio > 0.5 && aspectRatio < 2.0) {
                // 检查轮廓面积与边界矩形面积的比例
                double rectArea = boundingRect.width * boundingRect.height;
                double fillRatio = area / rectArea;

                if (fillRatio > 0.3) { // 填充比例合理
                    cv::Moments moments = cv::moments(contour);
                    if (moments.m00 != 0) {
                        cv::Point2f center(moments.m10 / moments.m00, moments.m01 / moments.m00);
                        finderCenters.push_back(center);
                        qDebug() << "Simple finder pattern detected at:" << center.x << "," << center.y;
                    }
                }
            }
        }

        // 过滤重复的点
        std::vector<cv::Point2f> filteredCenters;
        const double minDistance = 50.0;

        for (const auto& center : finderCenters) {
            bool isDuplicate = false;
            for (const auto& existing : filteredCenters) {
                double distance = cv::norm(center - existing);
                if (distance < minDistance) {
                    isDuplicate = true;
                    break;
                }
            }
            if (!isDuplicate) {
                filteredCenters.push_back(center);
            }
        }

        qDebug() << "Simple detection found" << filteredCenters.size() << "unique patterns";
        return filteredCenters;

    } catch (const std::exception& e) {
        qWarning() << "Exception in simple finder pattern detection:" << e.what();
    }

    return finderCenters;
}

std::vector<cv::Point2f> QRCodeDetector::detectFinderPatternsTemplate(const cv::Mat& grayImage)
{
    std::vector<cv::Point2f> finderCenters;

    if (grayImage.empty()) {
        return finderCenters;
    }

    try {
        qDebug() << "Starting template-based finder pattern detection";

        // 创建一个简单的定位图案模板（7x7像素的黑白黑模式）
        cv::Mat finderTemplate = cv::Mat::zeros(21, 21, CV_8UC1);

        // 外层黑色边框
        cv::rectangle(finderTemplate, cv::Point(0, 0), cv::Point(20, 20), cv::Scalar(0), -1);
        // 中层白色
        cv::rectangle(finderTemplate, cv::Point(3, 3), cv::Point(17, 17), cv::Scalar(255), -1);
        // 内层黑色
        cv::rectangle(finderTemplate, cv::Point(6, 6), cv::Point(14, 14), cv::Scalar(0), -1);

        // 在不同尺度下进行模板匹配
        std::vector<double> scales = {0.5, 0.7, 1.0, 1.5, 2.0, 3.0};

        for (double scale : scales) {
            try {
                // 缩放模板
                cv::Mat scaledTemplate;
                cv::resize(finderTemplate, scaledTemplate, cv::Size(), scale, scale, cv::INTER_NEAREST);

                if (scaledTemplate.cols > grayImage.cols || scaledTemplate.rows > grayImage.rows) {
                    continue; // 模板太大，跳过
                }

                // 执行模板匹配
                cv::Mat matchResult;
                cv::matchTemplate(grayImage, scaledTemplate, matchResult, cv::TM_CCOEFF_NORMED);

                // 查找匹配点
                double minVal, maxVal;
                cv::Point minLoc, maxLoc;
                cv::minMaxLoc(matchResult, &minVal, &maxVal, &minLoc, &maxLoc);

                // 设置阈值
                double threshold = 0.3; // 相对宽松的阈值

                if (maxVal > threshold) {
                    // 找到匹配，计算中心点
                    cv::Point2f center(maxLoc.x + scaledTemplate.cols / 2.0,
                                     maxLoc.y + scaledTemplate.rows / 2.0);

                    // 检查是否与已有的点重复
                    bool isDuplicate = false;
                    for (const auto& existing : finderCenters) {
                        double distance = cv::norm(center - existing);
                        if (distance < 30.0) {
                            isDuplicate = true;
                            break;
                        }
                    }

                    if (!isDuplicate) {
                        finderCenters.push_back(center);
                        qDebug() << "Template match found at:" << center.x << "," << center.y
                                 << "scale:" << scale << "confidence:" << maxVal;
                    }
                }

                // 查找多个匹配点
                cv::Mat thresholdMask;
                cv::threshold(matchResult, thresholdMask, threshold, 1.0, cv::THRESH_BINARY);

                std::vector<std::vector<cv::Point>> contours;
                cv::findContours(thresholdMask, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

                for (const auto& contour : contours) {
                    cv::Moments moments = cv::moments(contour);
                    if (moments.m00 > 0) {
                        cv::Point2f matchCenter(moments.m10 / moments.m00, moments.m01 / moments.m00);
                        cv::Point2f realCenter(matchCenter.x + scaledTemplate.cols / 2.0,
                                             matchCenter.y + scaledTemplate.rows / 2.0);

                        // 检查重复
                        bool isDuplicate = false;
                        for (const auto& existing : finderCenters) {
                            double distance = cv::norm(realCenter - existing);
                            if (distance < 30.0) {
                                isDuplicate = true;
                                break;
                            }
                        }

                        if (!isDuplicate && finderCenters.size() < 10) { // 限制最大数量
                            finderCenters.push_back(realCenter);
                            qDebug() << "Additional template match at:" << realCenter.x << "," << realCenter.y;
                        }
                    }
                }

            } catch (const std::exception& e) {
                qWarning() << "Exception in template matching at scale" << scale << ":" << e.what();
                continue;
            }
        }

        qDebug() << "Template-based detection found" << finderCenters.size() << "patterns";

    } catch (const std::exception& e) {
        qWarning() << "Exception in template-based finder pattern detection:" << e.what();
    }

    return finderCenters;
}
